import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../utils/number_formatter.dart';

class CustomerCard extends StatelessWidget {
  const CustomerCard({
    super.key,
    required this.customer,
    required this.onTap,
    this.onLongPress,
    this.isSelectionMode = false,
    this.isSelected = false,
    this.onSelectionChanged,
    this.index,
    this.isSearchMode = false,
    this.onDeletePressed,
    this.onSelectPressed,
    this.totalAmount,
    this.showPaymentsAmount =
        false, // true لعرض "إجمالي التسديدات"، false لعرض "إجمالي المبلغ"
    this.isPaymentsTab = false, // true إذا كانت البطاقة في قائمة التسديدات
    this.lastPayment, // آخر عملية تسديد
  });
  final Customer customer;
  final VoidCallback onTap;
  final VoidCallback? onLongPress;
  final bool isSelectionMode;
  final bool isSelected;
  final ValueChanged<bool>? onSelectionChanged;
  final int? index;
  final bool isSearchMode;
  final VoidCallback? onDeletePressed;
  final VoidCallback? onSelectPressed;
  final double? totalAmount;
  final bool showPaymentsAmount;
  final bool isPaymentsTab;
  final Map<String, dynamic>? lastPayment;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
          bottom: isPaymentsTab ? 8 : 16), // تقليل المسافة في قائمة التسديدات
      child: Material(
        elevation: 2,
        borderRadius: BorderRadius.circular(20),
        shadowColor: Colors.grey.withValues(alpha: 0.2),
        child: InkWell(
          onTap: isSelectionMode
              ? () => onSelectionChanged?.call(!isSelected)
              : onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: isSelected ? Colors.blue.shade50 : Colors.white,
              border: Border.all(
                color: isSelected ? Colors.blue.shade300 : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Stack(
              children: [
                // Simple serial number in top-left corner - إخفاء في قائمة التسديدات
                if (index != null && !isPaymentsTab)
                  Positioned(
                    top: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                        border: Border.all(
                          color: Colors.grey.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        '${index! + 1}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade700,
                          letterSpacing: 1.0,
                        ),
                      ),
                    ),
                  ),

                Padding(
                  padding: EdgeInsets.all(isPaymentsTab
                      ? 16
                      : 20), // تقليل الحشو في قائمة التسديدات
                  child: Column(
                    children: [
                      // Header Row
                      Row(
                        children: [
                          // Customer Avatar with decorative ring and icon - أصغر في قائمة التسديدات
                          Stack(
                            children: [
                              // Decorative ring
                              Container(
                                width: isPaymentsTab ? 40 : 48,
                                height: isPaymentsTab ? 40 : 48,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.blue.shade100,
                                      Colors.blue.shade50,
                                    ],
                                  ),
                                ),
                              ),
                              // Avatar
                              Positioned(
                                left: isPaymentsTab ? 2 : 3,
                                top: isPaymentsTab ? 2 : 3,
                                child: Container(
                                  width: isPaymentsTab ? 36 : 42,
                                  height: isPaymentsTab ? 36 : 42,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Colors.blue.shade600,
                                        Colors.blue.shade700,
                                      ],
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.blue.withValues(
                                          alpha: 0.3,
                                        ),
                                        blurRadius: isPaymentsTab ? 4 : 6,
                                        offset:
                                            Offset(0, isPaymentsTab ? 2 : 3),
                                      ),
                                    ],
                                  ),
                                  child: Center(
                                    child: Text(
                                      customer.name.isNotEmpty
                                          ? customer.name[0].toUpperCase()
                                          : 'ع',
                                      style: TextStyle(
                                        fontSize: isPaymentsTab ? 16 : 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              // Customer icon
                              Positioned(
                                right: 0,
                                bottom: 0,
                                child: Container(
                                  padding:
                                      EdgeInsets.all(isPaymentsTab ? 2 : 3),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.blue.shade200,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(
                                          alpha: 0.1,
                                        ),
                                        blurRadius: 3,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.person,
                                    size: isPaymentsTab ? 8 : 10,
                                    color: Colors.blue.shade600,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          SizedBox(width: isPaymentsTab ? 12 : 16),

                          // Customer Info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  customer.name,
                                  style: TextStyle(
                                    fontSize: isPaymentsTab ? 16 : 18,
                                    fontWeight: FontWeight.bold,
                                    color: const Color(0xFF2C3E50),
                                  ),
                                ),
                                SizedBox(height: isPaymentsTab ? 4 : 6),

                                // Phone number with call button - إخفاء في قائمة التسديدات
                                if (customer.phone != null &&
                                    customer.phone!.isNotEmpty &&
                                    !isPaymentsTab)
                                  Row(
                                    children: [
                                      Text(
                                        'هاتف: ${customer.phone!}',
                                        style: const TextStyle(
                                          fontSize: 13,
                                          color: Colors.black87,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      // Call button
                                      GestureDetector(
                                        onTap: () =>
                                            _makePhoneCall(customer.phone!),
                                        child: Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              colors: [
                                                Colors.green,
                                                Colors.green.shade600,
                                              ],
                                            ),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.green.withValues(
                                                  alpha: 0.3,
                                                ),
                                                blurRadius: 4,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                          child: const Icon(
                                            Icons.call,
                                            size: 14,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ),
                                      const Spacer(),
                                    ],
                                  ),
                              ],
                            ),
                          ),

                          // Action buttons for search mode or selection indicator
                          if (isSearchMode && !isSelectionMode) ...[
                            // Quick action buttons in search mode
                            Column(
                              children: [
                                // Delete button
                                GestureDetector(
                                  onTap: onDeletePressed,
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    margin: const EdgeInsets.only(bottom: 8),
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade100,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.red.shade300,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.delete,
                                      size: 16,
                                      color: Colors.red.shade600,
                                    ),
                                  ),
                                ),
                                // Select button
                                GestureDetector(
                                  onTap: onSelectPressed,
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.orange.shade100,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.orange.shade300,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.checklist,
                                      size: 16,
                                      color: Colors.orange.shade600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ] else if (isSelectionMode) ...[
                            // Selection indicator
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? Colors.blue
                                    : Colors.grey.shade200,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: isSelected
                                      ? Colors.blue.shade700
                                      : Colors.grey.shade400,
                                  width: 2,
                                ),
                              ),
                              child: Icon(
                                isSelected
                                    ? Icons.check
                                    : Icons.circle_outlined,
                                size: 16,
                                color: isSelected
                                    ? Colors.white
                                    : Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ],
                      ),

                      // Last Payment Info - فقط في قائمة التسديدات
                      if (isPaymentsTab && lastPayment != null) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.payment,
                                color: Colors.green.shade600,
                                size: 16,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'آخر تسديد:',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                              const Spacer(),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    _formatPaymentAmount(
                                        lastPayment!['amount']),
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green.shade700,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    _formatPaymentDateOnly(
                                        lastPayment!['date']),
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.grey.shade700,
                                    ),
                                  ),
                                  Text(
                                    _formatPaymentTimeOnly(
                                        lastPayment!['date']),
                                    style: TextStyle(
                                      fontSize: 9,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],

                      // Total Amount Display at bottom
                      if (totalAmount != null) ...[
                        SizedBox(height: isPaymentsTab ? 10 : 16),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: isPaymentsTab ? 12 : 16,
                            vertical: isPaymentsTab ? 8 : 12,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.account_balance_wallet,
                                color: Colors.blue.shade600,
                                size: isPaymentsTab ? 16 : 18,
                              ),
                              SizedBox(width: isPaymentsTab ? 6 : 8),
                              Text(
                                showPaymentsAmount
                                    ? 'إجمالي التسديدات:'
                                    : 'إجمالي المبلغ:',
                                style: TextStyle(
                                  fontSize: isPaymentsTab ? 12 : 13,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                NumberFormatter.formatCurrencyWithoutSymbol(
                                  totalAmount!.abs(),
                                ),
                                style: TextStyle(
                                  fontSize: isPaymentsTab ? 13 : 14,
                                  fontWeight: FontWeight.bold,
                                  color: totalAmount! >= 0
                                      ? Colors.green.shade700
                                      : Colors.red.shade700,
                                ),
                              ),
                              SizedBox(width: isPaymentsTab ? 3 : 4),
                              Icon(
                                totalAmount! >= 0
                                    ? Icons.trending_up
                                    : Icons.trending_down,
                                color: totalAmount! >= 0
                                    ? Colors.green.shade600
                                    : Colors.red.shade600,
                                size: isPaymentsTab ? 14 : 16,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      }
    } catch (e) {
      // Handle error silently or show a snackbar
      debugPrint('Could not launch $launchUri: $e');
    }
  }

  // تنسيق مبلغ التسديد
  String _formatPaymentAmount(dynamic amount) {
    if (amount == null) return '0';
    final double value =
        amount is double ? amount : double.tryParse(amount.toString()) ?? 0;
    return NumberFormatter.formatCurrencyWithoutSymbol(value);
  }

  // تنسيق التاريخ فقط مع اسم اليوم
  String _formatPaymentDateOnly(dynamic date) {
    if (date == null) return '';

    DateTime dateTime;
    if (date is DateTime) {
      dateTime = date;
    } else if (date is String) {
      dateTime = DateTime.tryParse(date) ?? DateTime.now();
    } else {
      return '';
    }

    final dayFormat = DateFormat('EEEE', 'ar'); // اسم اليوم بالعربية
    final dateFormat = DateFormat('dd/MM/yyyy'); // التاريخ بالأرقام الإنجليزية

    final dayName = dayFormat.format(dateTime);
    final formattedDate = dateFormat.format(dateTime);

    return '$dayName $formattedDate';
  }

  // تنسيق الوقت فقط مع الفترة العربية
  String _formatPaymentTimeOnly(dynamic date) {
    if (date == null) return '';

    DateTime dateTime;
    if (date is DateTime) {
      dateTime = date;
    } else if (date is String) {
      dateTime = DateTime.tryParse(date) ?? DateTime.now();
    } else {
      return '';
    }

    // تحويل إلى نظام 12 ساعة
    int hour12 = dateTime.hour;
    if (hour12 == 0) {
      hour12 = 12; // منتصف الليل
    } else if (hour12 > 12) {
      hour12 = hour12 - 12; // تحويل إلى PM
    }

    final formattedTime =
        '${hour12.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    final arabicPeriod = _getArabicTimePeriod(dateTime.hour);

    return '$formattedTime $arabicPeriod';
  }

  // تحديد فترة الوقت بالعربية
  String _getArabicTimePeriod(int hour) {
    if (hour >= 5 && hour < 12) {
      return 'صباحاً';
    } else if (hour >= 12 && hour < 14) {
      return 'ظهراً';
    } else if (hour >= 14 && hour < 17) {
      return 'عصراً';
    } else if (hour >= 17 && hour < 20) {
      return 'مساءً';
    } else if (hour >= 20 && hour < 24) {
      return 'ليلاً';
    } else {
      return 'فجراً'; // من منتصف الليل حتى الفجر
    }
  }
}
