import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/debt.dart';
import '../models/customer.dart';
import '../models/payment.dart';
import '../models/custom_card_type.dart';

import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../providers/customer_provider.dart';
import '../screens/add_debt_screen.dart';
import '../utils/number_formatter.dart';

class DebtCard extends StatelessWidget {
  const DebtCard({
    super.key,
    required this.debt,
    this.onStartMultiSelectForPayment,
    this.onStartMultiSelectForDelete,
    this.onToggleSelection, // إضافة callback للتحديد
    this.isSelected = false,
    this.isMultiSelectMode = false,
    this.isGridView = false,
    this.cardColor,
    this.isMiniView = false, // إضافة معلومة العرض المصغر
  });
  final Debt debt;
  final VoidCallback? onStartMultiSelectForPayment;
  final VoidCallback? onStartMultiSelectForDelete;
  final VoidCallback? onToggleSelection; // callback لتبديل التحديد
  final bool isSelected;
  final bool isMultiSelectMode;
  final bool isGridView;
  final Color? cardColor;
  final bool isMiniView; // هل هو عرض مصغر؟

  @override
  Widget build(BuildContext context) {
    return Consumer2<DebtProvider, CustomerProvider>(
      builder: (context, debtProvider, customerProvider, child) {
        // Get the latest debt data from provider for financial info only
        final currentDebt = debtProvider.debts.firstWhere(
          (d) => d.id == debt.id,
          orElse: () => debt, // Fallback to original debt if not found
        );

        // Get the latest customer data from provider
        final customer = customerProvider.customers.firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => Customer(
            id: debt.customerId,
            name: 'عميل غير موجود',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        // ALWAYS use original debt for dates - never use currentDebt for dates
        final isOverdue = DateTime.now().isAfter(debt.dueDate) &&
            currentDebt.status != DebtStatus.paid;
        final dayFormat = DateFormat('EEEE', 'ar');

        // تحديد ما إذا كانت البطاقة تحتوي على تفاصيل كثيرة
        final hasMultipleCardTypes = debt.cardType.startsWith('متعدد: ');
        final hasNotes = debt.notes != null && debt.notes!.isNotEmpty;
        final isCompact = hasMultipleCardTypes || hasNotes || isGridView;

        return GestureDetector(
          onTap: () {
            if (isMultiSelectMode) {
              // في وضع التحديد المتعدد، قم بتبديل التحديد
              if (onToggleSelection != null) {
                onToggleSelection!();
              }
            } else if (isMiniView) {
              // النافذة المنبثقة تعمل فقط في العرض المصغر
              _showDebtDetailsModal(context, debt, currentDebt);
            }
            // في العرض العادي والمضغوط، لا تفعل شيئاً عند النقر العادي
          },
          onLongPress: () {
            if (!isMultiSelectMode) {
              _showDebtOptionsDialog(context, debt, currentDebt);
            }
          },
          child: Container(
            margin: EdgeInsets.only(
              bottom: isGridView ? 0 : (isCompact ? 8 : 12),
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? Colors.blue.shade400 : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: isSelected
                      ? Colors.blue.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: isCompact ? 0.15 : 0.2),
                  blurRadius: isSelected ? 8 : (isCompact ? 3 : 4),
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: isGridView ? MainAxisSize.min : MainAxisSize.max,
              children: [
                // Customer Name Bar
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(isGridView ? 6 : 12),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Colors.blue.shade600
                        : cardColor ??
                            _getCustomerNameBarColor(
                              debt,
                              currentDebt.status,
                            ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Selection indicator in multi-select mode
                      if (isMultiSelectMode) ...[
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color:
                                isSelected ? Colors.white : Colors.transparent,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                          child: isSelected
                              ? Icon(
                                  Icons.check,
                                  color: Colors.blue.shade600,
                                  size: 16,
                                )
                              : null,
                        ),
                        const SizedBox(width: 12),
                      ],
                      // Warning icon for overdue debts
                      if (isOverdue && !isMultiSelectMode) ...[
                        Icon(
                          Icons.warning,
                          color: Colors.white,
                          size: isGridView ? 14 : 18,
                        ),
                        const SizedBox(width: 8),
                      ],
                      // Touch icon in grid view
                      if (isGridView && !isMultiSelectMode) ...[
                        const Icon(
                          Icons.touch_app,
                          color: Colors.white,
                          size: 14,
                        ),
                        const SizedBox(width: 8),
                      ],
                      Expanded(
                        child: Text(
                          customer.name,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: isGridView ? 12 : 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // حالة البيع (اليوم/أمس)
                      if (isGridView &&
                          _getSaleDateIndicator(debt).isNotEmpty) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _getSaleDateIndicator(debt),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      // Date indicator (Today/Yesterday) - مخفي في العرض الشبكي
                      if (!isGridView &&
                          _getSaleDateIndicator(debt).isNotEmpty) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            _getSaleDateIndicator(debt),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      // Additional warning icon for overdue debts
                      if (isOverdue) ...[
                        const Icon(
                          Icons.error_outline,
                          color: Colors.white,
                          size: 18,
                        ),
                      ],
                    ],
                  ),
                ),

                // Content
                Padding(
                  padding: EdgeInsets.all(
                    isGridView ? 3 : (isCompact ? 10 : 12),
                  ),
                  child: Column(
                    mainAxisSize:
                        isGridView ? MainAxisSize.min : MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Time Counters - مخفي في العرض الشبكي
                      if (!isGridView)
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.blue.shade200,
                                    width: 1.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.blue.withValues(alpha: 0.1),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: _buildTimeCounter(
                                  'منذ القيد',
                                  _getDaysSinceEntry(debt),
                                  Icons.event_available,
                                  const Color(
                                      0xFF1A237E), // لون حكومي أزرق داكن
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.orange.shade200,
                                    width: 1.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.orange.withValues(
                                        alpha: 0.1,
                                      ),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: _buildTimeCounter(
                                  _getTimeCounterLabel(debt),
                                  _getDaysUntilDue(debt),
                                  Icons.alarm,
                                  const Color(
                                      0xFF1A237E), // لون حكومي أزرق داكن
                                ),
                              ),
                            ),
                          ],
                        ),

                      SizedBox(height: isGridView ? 2 : 12),

                      // Time Counters - في العرض الشبكي بعد شريط العميل
                      if (isGridView)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 2,
                            vertical: 1,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: Colors.grey.shade200,
                              width: 0.3,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Expanded(
                                child: _buildTimeCounter(
                                  'منذ القيد',
                                  _getDaysSinceEntry(debt),
                                  Icons.access_time,
                                  const Color(
                                      0xFF1A237E), // لون حكومي أزرق داكن
                                  isCompact: true,
                                ),
                              ),
                              Container(
                                width: 0.3,
                                height: 8,
                                color: Colors.grey.shade300,
                              ),
                              Expanded(
                                child: _buildTimeCounter(
                                  _getTimeCounterLabel(debt),
                                  _getDaysUntilDue(debt),
                                  Icons.schedule,
                                  const Color(
                                      0xFF1A237E), // لون حكومي أزرق داكن
                                  isCompact: true,
                                ),
                              ),
                            ],
                          ),
                        ),

                      SizedBox(height: isGridView ? 1 : 0),

                      // Header - مبسط في العرض الشبكي
                      if (isGridView)
                        // عرض مبسط للشبكة
                        Column(
                          children: [
                            // نوع الكارت والكمية
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // نوع الكارت
                                Expanded(
                                  child: Consumer<CardTypeProvider>(
                                    builder: (context, cardTypeProvider, _) {
                                      final displayName =
                                          _getCardTypeDisplayName(
                                        debt.cardType,
                                        cardTypeProvider,
                                      );
                                      return Text(
                                        displayName,
                                        style: TextStyle(
                                          color: _getCardTypeColor(
                                            debt.cardType,
                                          ),
                                          fontSize: 10,
                                          fontWeight: FontWeight.w600,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      );
                                    },
                                  ),
                                ),
                                // الكمية
                                Flexible(
                                  child: Text(
                                    'الكمية ${NumberFormatter.formatNumber(debt.quantity)}',
                                    style: const TextStyle(
                                      fontSize: 8,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xFF2C3E50),
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      const SizedBox(height: 2),

                      SizedBox(height: isGridView ? 2 : 12),

                      // Header - العرض العادي
                      if (!isGridView)
                        // العرض العادي
                        Row(
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.inventory_2,
                                    size: 18,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'الكمية: ${NumberFormatter.formatNumber(debt.quantity)}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Color(0xFF2C3E50),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor(
                                  currentDebt.status,
                                ).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: _getStatusColor(
                                    currentDebt.status,
                                  ).withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                currentDebt.status.displayName,
                                style: TextStyle(
                                  color: _getStatusColor(currentDebt.status),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),

                      SizedBox(height: isGridView ? 2 : 12),

                      // Amount Info - مبسط في العرض الشبكي
                      Container(
                        padding: EdgeInsets.all(isGridView ? 2 : 12),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            // المبلغ الإجمالي
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  isGridView ? 'الإجمالي:' : 'المبلغ الإجمالي:',
                                  style: TextStyle(
                                    color: Colors.black87,
                                    fontWeight: FontWeight.w500,
                                    fontSize: isGridView ? 8 : 14,
                                  ),
                                ),
                                Flexible(
                                  child: Text(
                                    NumberFormatter.formatCurrency(debt.amount),
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                      fontSize: isGridView ? 9 : 14,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            // المبلغ المتبقي (الأهم في العرض الشبكي)
                            if (currentDebt.remainingAmount > 0) ...[
                              SizedBox(height: isGridView ? 0.5 : 4),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    isGridView ? 'المتبقي:' : 'المبلغ المتبقي:',
                                    style: TextStyle(
                                      color: Colors.black87,
                                      fontWeight: FontWeight.w500,
                                      fontSize: isGridView ? 8 : 14,
                                    ),
                                  ),
                                  Flexible(
                                    child: Text(
                                      NumberFormatter.formatCurrency(
                                        currentDebt.remainingAmount,
                                      ),
                                      style: TextStyle(
                                        color: isOverdue
                                            ? Colors.red
                                            : Colors.orange,
                                        fontWeight: FontWeight.bold,
                                        fontSize: isGridView ? 9 : 14,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                            // المبلغ المدفوع (مخفي في العرض الشبكي إذا لم يكن مهم)
                            if (!isGridView && currentDebt.paidAmount > 0) ...[
                              const SizedBox(height: 4),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'المبلغ المدفوع:',
                                    style: TextStyle(
                                      color: Colors.black87,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    NumberFormatter.formatCurrency(
                                      currentDebt.paidAmount,
                                    ),
                                    style: const TextStyle(
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),

                      SizedBox(height: isGridView ? 1 : 12),

                      // Card Type and Dates
                      if (isGridView)
                        // عرض مبسط للتواريخ في الشبكة مع اسم اليوم والوقت
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // تاريخ القيد
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'القيد:',
                                    style: TextStyle(
                                      fontSize: 7,
                                      color: Colors.black87,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    dayFormat.format(debt.entryDate),
                                    style: const TextStyle(
                                      fontSize: 7,
                                      color: Colors.blue,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  Text(
                                    _formatDateArabic(debt.entryDate),
                                    style: const TextStyle(
                                      fontSize: 7,
                                      color: Colors.blue,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    _formatTime(debt.entryDate),
                                    style: const TextStyle(
                                      fontSize: 6,
                                      color: Colors.grey,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // تاريخ الاستحقاق
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'الاستحقاق:',
                                    style: TextStyle(
                                      fontSize: 7,
                                      color: isOverdue
                                          ? Colors.red[400]
                                          : Colors.black87,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    dayFormat.format(debt.dueDate),
                                    style: TextStyle(
                                      fontSize: 7,
                                      color: isOverdue
                                          ? Colors.red[300]
                                          : Colors.orange,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  Text(
                                    _formatDateArabic(debt.dueDate),
                                    style: TextStyle(
                                      fontSize: 7,
                                      color: isOverdue
                                          ? Colors.red
                                          : Colors.orange,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        )
                      else ...[
                        // العرض العادي الكامل
                        Row(
                          children: [
                            // Card Type Section (moved to left)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Card type header

                                // Card type value
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Consumer<CardTypeProvider>(
                                    builder: (context, cardTypeProvider, _) {
                                      // إذا كان نوع مختلط، عرض قائمة منفصلة
                                      if (debt.cardType.startsWith('متعدد: ')) {
                                        return _buildMixedCardTypesList(
                                          debt.cardType,
                                        );
                                      }

                                      final displayName =
                                          _getCardTypeDisplayName(
                                        debt.cardType,
                                        cardTypeProvider,
                                      );

                                      return Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(4),
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                colors: [
                                                  _getCardTypeColor(
                                                    debt.cardType,
                                                  ).withValues(alpha: 0.8),
                                                  _getCardTypeColor(
                                                    debt.cardType,
                                                  ),
                                                ],
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: _getCardTypeColor(
                                                    debt.cardType,
                                                  ).withValues(alpha: 0.3),
                                                  blurRadius: 4,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                            child: const Icon(
                                              Icons.credit_card_rounded,
                                              size: 14,
                                              color: Colors.white,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          Flexible(
                                            child: Text(
                                              displayName,
                                              style: TextStyle(
                                                color: _getCardTypeColor(
                                                  debt.cardType,
                                                ),
                                                fontSize: 12,
                                                fontWeight: FontWeight.w600,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          // إضافة الكمية للكارت المفرد
                                          const SizedBox(width: 6),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 6,
                                              vertical: 2,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.blue.shade100,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              border: Border.all(
                                                color: Colors.blue.shade300,
                                              ),
                                            ),
                                            child: Text(
                                              '${debt.quantity}',
                                              style: TextStyle(
                                                color: Colors.blue.shade700,
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Dates Section
                        Row(
                          children: [
                            // تاريخ القيد
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Colors.blue
                                              .withValues(alpha: 0.1),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                        ),
                                        child: const Icon(
                                          Icons.event_note,
                                          size: 16,
                                          color: Colors.blue,
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      const Text(
                                        'تاريخ القيد:',
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: Colors.black54,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      Text(
                                        dayFormat.format(
                                          debt.entryDate,
                                        ),
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.blue,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        _formatDateArabic(
                                          debt.entryDate,
                                        ),
                                        style: const TextStyle(
                                          fontSize: 13,
                                          color: Colors.black87,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Text(
                                    _formatTime(debt.entryDate),
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.black54,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // فاصل
                            Container(
                              width: 1,
                              height: 60,
                              color: Colors.grey.shade300,
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 16),
                            ),

                            // تاريخ الاستحقاق
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Text(
                                        'تاريخ الاستحقاق:',
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: isOverdue
                                              ? Colors.red[400]
                                              : Colors.black54,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: (isOverdue
                                                  ? Colors.red
                                                  : Colors.orange)
                                              .withValues(alpha: 0.1),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                        ),
                                        child: Icon(
                                          Icons.schedule,
                                          size: 16,
                                          color: isOverdue
                                              ? Colors.red
                                              : Colors.orange,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Text(
                                        _formatDateArabic(
                                          debt.dueDate,
                                        ),
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: isOverdue
                                              ? Colors.red
                                              : Colors.black87,
                                          fontWeight: isOverdue
                                              ? FontWeight.bold
                                              : FontWeight.w600,
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        dayFormat.format(
                                          debt.dueDate,
                                        ),
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: isOverdue
                                              ? Colors.red[300]
                                              : Colors.orange,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        if (debt.notes != null && debt.notes!.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Text(
                            'ملاحظات: ${debt.notes}', // استخدام البيانات الأصلية
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.black87,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],

                        const SizedBox(height: 16),

                        // Action Buttons - Only show for unpaid debts
                        Row(
                          children: [
                            IconButton(
                              onPressed: () => _editDebt(context, currentDebt),
                              icon: const Icon(Icons.edit),
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.blue.withValues(
                                  alpha: 0.1,
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: () =>
                                  _deleteDebt(context, currentDebt),
                              icon: const Icon(Icons.delete),
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.red.withValues(
                                  alpha: 0.1,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () => _showPaymentDialog(
                                  context,
                                  false,
                                  currentDebt,
                                ),
                                icon: const Icon(Icons.payment, size: 18),
                                label: const Text('تسديد'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 10,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ], // إغلاق قوس if (!isGridView)
                      // Action Buttons for grid view - ممتدة إلى حافة البطاقة
                      if (isGridView) ...[
                        const SizedBox(
                          height: 4,
                        ), // مسافة مقللة بين التواريخ والأزرار
                      ],
                    ],
                  ),
                ),

                // الأزرار خارج المحتوى الرئيسي لتمتد إلى حافة البطاقة
                if (isGridView) ...[
                  Container(
                    margin: const EdgeInsets.fromLTRB(
                      6,
                      0,
                      6,
                      0,
                    ), // هوامش صغيرة من الجوانب فقط
                    child: Row(
                      children: [
                        // زر التعديل
                        Expanded(
                          child: Container(
                            height: 28,
                            margin: const EdgeInsets.only(right: 2),
                            child: ElevatedButton(
                              onPressed: () => _editDebt(context, currentDebt),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue.withValues(
                                  alpha: 0.1,
                                ),
                                foregroundColor: Colors.blue,
                                padding: EdgeInsets.zero,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                elevation: 0,
                                minimumSize: Size.zero,
                              ),
                              child: const Icon(Icons.edit, size: 14),
                            ),
                          ),
                        ),
                        // زر الحذف
                        Expanded(
                          child: Container(
                            height: 28,
                            margin: const EdgeInsets.symmetric(horizontal: 2),
                            child: ElevatedButton(
                              onPressed: () =>
                                  _deleteDebt(context, currentDebt),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red.withValues(
                                  alpha: 0.1,
                                ),
                                foregroundColor: Colors.red,
                                padding: EdgeInsets.zero,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                elevation: 0,
                                minimumSize: Size.zero,
                              ),
                              child: const Icon(Icons.delete, size: 14),
                            ),
                          ),
                        ),
                        // زر التسديد
                        Expanded(
                          flex: 2,
                          child: Container(
                            height: 28,
                            margin: const EdgeInsets.only(left: 2),
                            child: ElevatedButton(
                              onPressed: () => _showPaymentDialog(
                                context,
                                false,
                                currentDebt,
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                                padding: EdgeInsets.zero,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                minimumSize: Size.zero,
                              ),
                              child: const Text(
                                'تسديد',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getStatusColor(DebtStatus status) {
    switch (status) {
      case DebtStatus.pending:
        return Colors.orange;
      case DebtStatus.partiallyPaid:
        return Colors.blue;
      case DebtStatus.paid:
        return Colors.green;
    }
  }

  Color _getCardTypeColor(String cardType) {
    // Check if it's a default card type
    if (cardType == CardType.cash.name) {
      return Colors.green;
    } else if (cardType == CardType.visa.name) {
      return Colors.blue;
    } else if (cardType == CardType.mastercard.name) {
      return Colors.red;
    } else if (cardType == CardType.americanExpress.name) {
      return Colors.purple;
    } else if (cardType.startsWith('custom_')) {
      return Colors.teal; // Color for custom types
    } else if (cardType.startsWith('متعدد: ')) {
      return Colors.purple; // Color for mixed types
    } else {
      return Colors.orange; // Default fallback
    }
  }

  // دالة لبناء قائمة الكارتات المختلطة بنفس تصميم النوع المفرد
  Widget _buildMixedCardTypesList(String cardType) {
    // إزالة نص "متعدد:" إذا كان موجود
    String cleanItemName = cardType;
    if (cardType.startsWith('متعدد: ')) {
      cleanItemName = cardType.substring(7); // إزالة "متعدد: "
    }

    // تقسيم النص إلى قائمة من الكارتات
    final cardTypes = cleanItemName.split(', ');

    // عرض كل كارت في سطر منفصل بنفس شكل الكارت الفردي
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: cardTypes.map((cardType) {
        // استخراج اسم الكارت والكمية من النص مثل "زين (3)"
        final match = RegExp(r'^(.+) \((\d+)\)$').firstMatch(cardType.trim());
        String name;
        String displayText;
        String? quantity;

        if (match != null) {
          name = match.group(1)!;
          quantity = match.group(2)!;
          displayText = _getCardTypeDisplayNameWithCategory(name);
        } else {
          name = cardType.trim();
          displayText = _getCardTypeDisplayNameWithCategory(name);
        }

        // بطاقة بدون حواف خارجية - فقط المحتوى الداخلي
        return Container(
          margin: const EdgeInsets.only(bottom: 4),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      _getCardTypeColorByName(name).withValues(alpha: 0.8),
                      _getCardTypeColorByName(name),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(6),
                  boxShadow: [
                    BoxShadow(
                      color: _getCardTypeColorByName(
                        name,
                      ).withValues(alpha: 0.3),
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.credit_card_rounded,
                  size: 12,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  displayText,
                  style: TextStyle(
                    color: Colors.grey.shade700,
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // إضافة الكمية إذا كانت موجودة
              if (quantity != null) ...[
                const SizedBox(width: 6),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade300),
                  ),
                  child: Text(
                    quantity,
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      }).toList(),
    );
  }

  // دالة للحصول على لون الكارت بالاسم
  Color _getCardTypeColorByName(String cardName) {
    final cleanName = cardName.toLowerCase().trim();

    switch (cleanName) {
      case 'زين':
      case 'zain':
        return Colors.purple;
      case 'آسيا':
      case 'اسيا':
      case 'asia':
      case 'sia':
        return Colors.red;
      case 'أبو الستة':
      case 'ابو الستة':
      case 'abusitta':
        return Colors.cyan;
      case 'أبو العشرة':
      case 'ابو العشرة':
      case 'abuashara':
        return Colors.cyan;
      default:
        return Colors.blue; // لون افتراضي
    }
  }

  // دالة للحصول على اسم الكارت
  String _getCardTypeDisplayNameWithCategory(String cardName) {
    final cleanName = cardName.toLowerCase().trim();

    switch (cleanName) {
      case 'زين':
      case 'zain':
        return 'زين';
      case 'آسيا':
      case 'اسيا':
      case 'asia':
      case 'sia':
        return 'آسيا';
      case 'أبو الستة':
      case 'ابو الستة':
      case 'abusitta':
        return 'أبو الستة';
      case 'أبو العشرة':
      case 'ابو العشرة':
      case 'abuashara':
        return 'أبو العشرة';
      default:
        return cardName; // للأنواع الأخرى
    }
  }

  // دالة للحصول على اسم نوع الكارت للعرض
  String _getCardTypeDisplayName(
    String cardType,
    CardTypeProvider cardTypeProvider,
  ) {
    // إذا كان نوع افتراضي
    if (cardType == CardType.cash.name) {
      return 'نقدي';
    } else if (cardType == CardType.visa.name) {
      return 'فيزا';
    } else if (cardType == CardType.mastercard.name) {
      return 'ماستركارد';
    } else if (cardType == CardType.americanExpress.name) {
      return 'أمريكان إكسبريس';
    } else if (cardType == CardType.zain.name) {
      return 'زين';
    } else if (cardType == CardType.sia.name) {
      return 'آسيا';
    } else if (cardType == CardType.abuAshara.name) {
      return 'أبو العشرة';
    } else if (cardType == CardType.abuSitta.name) {
      return 'أبو الستة';
    }

    // إذا كان نوع مخصص
    if (cardType.startsWith('custom_')) {
      try {
        final cardTypeId = int.parse(cardType.replaceFirst('custom_', ''));
        final customCardType = cardTypeProvider.customCardTypes.firstWhere(
          (ct) => ct.id == cardTypeId,
          orElse: () => throw Exception('Card type not found'),
        );
        return customCardType.displayName;
      } catch (e) {
        return 'نوع غير معروف';
      }
    }

    // إذا كان اسم مباشر (للديون المدمجة) - إزالة نص "متعدد:"
    String cleanCardType = cardType;
    if (cardType.startsWith('متعدد: ')) {
      cleanCardType = cardType.substring(7); // إزالة "متعدد: "
    }

    return cleanCardType.isNotEmpty ? cleanCardType : 'نوع غير معروف';
  }

  String _getDaysSinceEntry(Debt originalDebt) {
    final now = DateTime.now();
    final entryDate = originalDebt.entryDate;
    final difference = now.difference(entryDate);

    if (difference.inDays == 0) {
      // إذا كان اليوم، اعرض الساعات والدقائق
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;

      if (hours == 0) {
        return '$minutes دقيقة';
      } else {
        return '$hours ساعة و $minutes دقيقة';
      }
    } else if (difference.inDays == 1) {
      return '1 يوم';
    } else {
      return '${difference.inDays} يوم';
    }
  }

  // دالة لتحديد نص العداد حسب حالة الموعد
  String _getTimeCounterLabel(Debt originalDebt) {
    final now = DateTime.now();
    final dueDate = originalDebt.dueDate;
    final difference = dueDate.difference(now).inDays;

    if (difference < 0) {
      return 'تجاوز الموعد';
    } else {
      return 'متبقي للموعد';
    }
  }

  String _getDaysUntilDue(Debt originalDebt) {
    final now = DateTime.now();
    final dueDate = originalDebt.dueDate;
    final difference = dueDate.difference(now).inDays;

    if (difference < 0) {
      final overdue = difference.abs();
      if (overdue == 1) {
        return 'متأخر يوم';
      } else {
        return 'متأخر $overdue يوم';
      }
    } else if (difference == 0) {
      return 'اليوم';
    } else if (difference == 1) {
      return 'يوم واحد';
    } else {
      return '$difference يوم';
    }
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;

    // Convert to 12-hour format
    final hour12 = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    final period = hour < 12 ? 'صباحاً' : 'مساءً';

    return '${hour12.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  String _formatDateArabic(DateTime date) {
    return '${date.day}\\${date.month}\\${date.year}';
  }

  // Helper method to build time counter widget
  Widget _buildTimeCounter(
    String label,
    String value,
    IconData icon,
    Color color, {
    bool isCompact = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: isCompact ? 11 : 16),
        SizedBox(height: isCompact ? 0.5 : 4),
        Text(
          value,
          style: TextStyle(
            fontSize: isCompact ? 11 : 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: isCompact ? 0.5 : 2),
        Text(
          label,
          style: TextStyle(
            fontSize: isCompact ? 11 : 10,
            color: color.withValues(alpha: 0.8),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _showPaymentDialog(
    BuildContext context,
    bool initialIsFullPayment,
    Debt currentDebt,
  ) {
    final amountController = TextEditingController();
    final notesController = TextEditingController();
    DateTime selectedDate = DateTime.now();
    double remainingAmount = currentDebt.remainingAmount;
    bool isFullPayment = initialIsFullPayment;

    if (isFullPayment) {
      final formatter = NumberFormat('#,###', 'en');
      amountController.text = formatter.format(
        currentDebt.remainingAmount.round(),
      );
    }

    // Function to update remaining amount
    void updateRemainingAmount() {
      // إزالة الفواصل من النص قبل التحويل إلى رقم
      final cleanText = amountController.text.replaceAll(',', '');
      final enteredAmount = double.tryParse(cleanText) ?? 0;
      remainingAmount = currentDebt.remainingAmount - enteredAmount;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 40,
          ),
          elevation: 16,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.85,
            constraints: const BoxConstraints(maxWidth: 400),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white,
                  isFullPayment ? Colors.green.shade50 : Colors.orange.shade50,
                ],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header with icon
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: isFullPayment
                                  ? [Colors.green, Colors.green.shade600]
                                  : [Colors.orange, Colors.orange.shade600],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: (isFullPayment
                                        ? Colors.green
                                        : Colors.orange)
                                    .withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Icon(
                            isFullPayment ? Icons.payment : Icons.payments,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                isFullPayment ? 'تسديد كامل' : 'تسديد جزئي',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                debt.itemName, // استخدام البيانات الأصلية
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Payment Type Selector
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.grey.shade50,
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = true;
                                  final formatter = NumberFormat('#,###', 'en');
                                  amountController.text = formatter.format(
                                    currentDebt.remainingAmount.round(),
                                  );
                                  updateRemainingAmount();
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(12),
                                    bottomLeft: Radius.circular(12),
                                  ),
                                  gradient: isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.green,
                                            Colors.green.shade600,
                                          ],
                                        )
                                      : null,
                                  color:
                                      isFullPayment ? null : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payment,
                                      color: isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد كامل',
                                      style: TextStyle(
                                        color: isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  isFullPayment = false;
                                  amountController.clear();
                                  updateRemainingAmount();
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(12),
                                    bottomRight: Radius.circular(12),
                                  ),
                                  gradient: !isFullPayment
                                      ? LinearGradient(
                                          colors: [
                                            Colors.orange,
                                            Colors.orange.shade600,
                                          ],
                                        )
                                      : null,
                                  color: !isFullPayment
                                      ? null
                                      : Colors.transparent,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.payments,
                                      color: !isFullPayment
                                          ? Colors.white
                                          : Colors.grey[600],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تسديد جزئي',
                                      style: TextStyle(
                                        color: !isFullPayment
                                            ? Colors.white
                                            : Colors.grey[600],
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Debt Summary Card
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade50, Colors.blue.shade100],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'المبلغ المتبقي:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF2C3E50),
                                ),
                              ),
                              Text(
                                NumberFormatter.formatCurrency(
                                  currentDebt.remainingAmount,
                                ),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          ),
                          if (!isFullPayment) ...[
                            const SizedBox(height: 12),

                            // Progress Bar
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text(
                                      'تقدم التسديد:',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey,
                                      ),
                                    ),
                                    Text(
                                      '${((currentDebt.amount - remainingAmount) / currentDebt.amount * 100).toStringAsFixed(1)}%',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 6),
                                Container(
                                  height: 8,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.grey.shade200,
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(4),
                                    child: LinearProgressIndicator(
                                      value: (currentDebt.amount -
                                              remainingAmount) /
                                          currentDebt.amount,
                                      backgroundColor: Colors.transparent,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        remainingAmount <= 0
                                            ? Colors.green
                                            : Colors.blue,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'إجمالي الدين:',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                                Text(
                                  NumberFormatter.formatCurrency(
                                    debt.amount,
                                  ), // استخدام البيانات الأصلية
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'المتبقي بعد التسديد:',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black87,
                                  ),
                                ),
                                Text(
                                  NumberFormatter.formatCurrency(
                                    remainingAmount,
                                  ),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Amount Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color:
                            isFullPayment ? Colors.grey.shade50 : Colors.white,
                      ),
                      child: TextField(
                        controller: amountController,
                        keyboardType: TextInputType.number,
                        readOnly: isFullPayment,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          TextInputFormatter.withFunction((oldValue, newValue) {
                            if (newValue.text.isEmpty) {
                              return newValue;
                            }

                            // إزالة الفواصل الموجودة
                            final String cleanText = newValue.text.replaceAll(
                              ',',
                              '',
                            );

                            // تحويل إلى رقم وإضافة فواصل الآلاف
                            if (cleanText.isNotEmpty) {
                              final number = int.tryParse(cleanText);
                              if (number != null) {
                                final formatter = NumberFormat('#,###', 'en');
                                final formattedText = formatter.format(number);

                                return TextEditingValue(
                                  text: formattedText,
                                  selection: TextSelection.collapsed(
                                    offset: formattedText.length,
                                  ),
                                );
                              }
                            }

                            return newValue;
                          }),
                        ],
                        onChanged: (value) {
                          setState(() {
                            updateRemainingAmount();
                          });
                        },
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color:
                              isFullPayment ? Colors.grey[600] : Colors.black,
                        ),
                        decoration: InputDecoration(
                          labelText: 'مبلغ التسديد',
                          labelStyle: TextStyle(
                            color:
                                isFullPayment ? Colors.grey[600] : Colors.blue,
                            fontWeight: FontWeight.w600,
                          ),
                          suffixText: 'د.ع',
                          suffixStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.bold,
                          ),
                          prefixIcon: Icon(
                            Icons.attach_money,
                            color:
                                isFullPayment ? Colors.grey[600] : Colors.blue,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Date Picker
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                      child: InkWell(
                        onTap: () async {
                          final DateTime? picked = await showDatePicker(
                            context: context,
                            initialDate: selectedDate,
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now().add(
                              const Duration(days: 30),
                            ),
                            locale: const Locale('ar'),
                            builder: (context, child) {
                              return Theme(
                                data: Theme.of(context).copyWith(
                                  colorScheme: ColorScheme.light(
                                    primary: isFullPayment
                                        ? Colors.green
                                        : Colors.orange,
                                    onSurface: Colors.black87,
                                  ),
                                  // تخصيص حجم وتصميم منتقي التاريخ للهاتف
                                  datePickerTheme: DatePickerThemeData(
                                    backgroundColor: Colors.white,
                                    elevation: 8,
                                    shadowColor: Colors.black26,
                                    surfaceTintColor: Colors.transparent,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    headerBackgroundColor: isFullPayment
                                        ? Colors.green.shade600
                                        : Colors.orange.shade600,
                                    headerForegroundColor: Colors.white,
                                    headerHeadlineStyle: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    headerHelpStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    weekdayStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    dayStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    todayBackgroundColor:
                                        WidgetStateProperty.all(
                                      (isFullPayment
                                              ? Colors.green
                                              : Colors.orange)
                                          .withValues(alpha: 0.2),
                                    ),
                                    todayForegroundColor:
                                        WidgetStateProperty.all(
                                      isFullPayment
                                          ? Colors.green.shade700
                                          : Colors.orange.shade700,
                                    ),
                                    dayBackgroundColor:
                                        WidgetStateProperty.resolveWith((
                                      states,
                                    ) {
                                      if (states.contains(
                                        WidgetState.selected,
                                      )) {
                                        return isFullPayment
                                            ? Colors.green.shade600
                                            : Colors.orange.shade600;
                                      }
                                      return null;
                                    }),
                                    dayForegroundColor:
                                        WidgetStateProperty.resolveWith((
                                      states,
                                    ) {
                                      if (states.contains(
                                        WidgetState.selected,
                                      )) {
                                        return Colors.white;
                                      }
                                      return null;
                                    }),
                                  ),
                                ),
                                child: Transform.scale(
                                  scale: 1.1, // تكبير منتقي التاريخ بنسبة 10%
                                  child: child!,
                                ),
                              );
                            },
                          );
                          if (picked != null && picked != selectedDate) {
                            setState(() {
                              selectedDate = picked;
                            });
                          }
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                color: isFullPayment
                                    ? Colors.green
                                    : Colors.orange,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'تاريخ التسديد',
                                      style: TextStyle(
                                        color: isFullPayment
                                            ? Colors.green
                                            : Colors.orange,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      DateFormat(
                                        'yyyy/MM/dd',
                                      ).format(selectedDate),
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF2C3E50),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_drop_down,
                                color: Colors.grey[400],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Notes Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                      child: TextField(
                        controller: notesController,
                        maxLines: 2,
                        style: const TextStyle(
                          color: Colors.black87, // تحديد لون النص بوضوح
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          labelStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                          hintText: 'أضف أي ملاحظات حول التسديد...',
                          hintStyle: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 15,
                          ),
                          prefixIcon: Icon(
                            Icons.note_alt_outlined,
                            color: Colors.grey[600],
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: BorderSide(color: Colors.grey.shade400),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'إلغاء',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: () async {
                              // إزالة الفواصل من النص قبل التحويل إلى رقم
                              final cleanText =
                                  amountController.text.replaceAll(',', '');
                              final amount = double.tryParse(cleanText);
                              if (amount == null || amount <= 0) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: const Row(
                                      children: [
                                        Icon(Icons.error, color: Colors.white),
                                        SizedBox(width: 8),
                                        Text('يرجى إدخال مبلغ صحيح'),
                                      ],
                                    ),
                                    backgroundColor: Colors.red,
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                );
                                return;
                              }

                              if (amount > currentDebt.remainingAmount) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: const Row(
                                      children: [
                                        Icon(
                                          Icons.warning,
                                          color: Colors.white,
                                        ),
                                        SizedBox(width: 8),
                                        Text('المبلغ أكبر من المبلغ المتبقي'),
                                      ],
                                    ),
                                    backgroundColor: Colors.orange,
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                );
                                return;
                              }

                              try {
                                final debtProvider = Provider.of<DebtProvider>(
                                  context,
                                  listen: false,
                                );

                                await debtProvider.makePayment(
                                  currentDebt.id!,
                                  amount,
                                  isFullPayment
                                      ? PaymentType.full
                                      : PaymentType.partial,
                                  notesController.text.trim().isEmpty
                                      ? null
                                      : notesController.text.trim(),
                                  paymentDate: selectedDate,
                                );

                                if (context.mounted) {
                                  Navigator.pop(context);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Row(
                                        children: [
                                          const Icon(
                                            Icons.check_circle,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            isFullPayment
                                                ? 'تم التسديد الكامل بنجاح'
                                                : 'تم التسديد الجزئي بنجاح',
                                          ),
                                        ],
                                      ),
                                      backgroundColor: Colors.green,
                                      behavior: SnackBarBehavior.floating,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  );
                                }
                              } catch (e) {
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Row(
                                        children: [
                                          const Icon(
                                            Icons.error,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              'حدث خطأ: ${e.toString()}',
                                            ),
                                          ),
                                        ],
                                      ),
                                      backgroundColor: Colors.red,
                                      behavior: SnackBarBehavior.floating,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                  );
                                }
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  isFullPayment ? Colors.green : Colors.orange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 4,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  isFullPayment
                                      ? Icons.payment
                                      : Icons.payments,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  isFullPayment ? 'تسديد كامل' : 'تسديد جزئي',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _editDebt(BuildContext context, Debt currentDebt) async {
    // الحصول على أحدث بيانات العميل قبل التعديل
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );

    // تحديث قائمة العملاء للحصول على أحدث البيانات
    await customerProvider.loadCustomers();

    // الحصول على العميل المحدث
    final updatedCustomer = customerProvider.customers.firstWhere(
      (c) => c.id == debt.customerId,
      orElse: () => Customer(
        id: debt.customerId,
        name: 'عميل غير موجود',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    if (context.mounted) {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              AddDebtScreen(customer: updatedCustomer, debt: currentDebt),
        ),
      );

      // تحديث البيانات بعد العودة من شاشة التعديل
      if (result == true && context.mounted) {
        // الحصول على DebtProvider قبل العمليات غير المتزامنة
        final debtProvider = Provider.of<DebtProvider>(context, listen: false);

        await customerProvider.loadCustomers();
        // تحديث قائمة الديون أيضاً
        await debtProvider.refreshCurrentCustomerDebts();
      }
    }
  }

  void _deleteDebt(BuildContext context, Debt currentDebt) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 20,
        backgroundColor: Colors.white,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 450),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.delete_forever,
                  color: Colors.red.shade600,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              // Title
              const Text(
                'حذف الدين',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Content
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
                ),
                child: Column(
                  children: [
                    Text(
                      'هل أنت متأكد من حذف الدين "${currentDebt.itemName}"؟',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا يمكن التراجع عن هذا الإجراء',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        side: BorderSide(color: Colors.grey.shade400),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        try {
                          await Provider.of<DebtProvider>(
                            context,
                            listen: false,
                          ).deleteDebt(currentDebt.id!);

                          if (context.mounted) {
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('تم حذف الدين بنجاح'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        } catch (e) {
                          if (context.mounted) {
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('حدث خطأ: ${e.toString()}'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        'حذف الدين',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تحديد مؤشر تاريخ البيع (اليوم/الأمس)
  String _getSaleDateIndicator(Debt originalDebt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final entryDate = DateTime(
      originalDebt.entryDate.year,
      originalDebt.entryDate.month,
      originalDebt.entryDate.day,
    );

    if (entryDate.isAtSameMomentAs(today)) {
      return 'اليوم';
    } else if (entryDate.isAtSameMomentAs(yesterday)) {
      return 'الأمس';
    } else {
      return ''; // لا يظهر مؤشر للأيام الأخرى
    }
  }

  // تحديد لون شريط اسم العميل حسب حالة الدين
  Color _getCustomerNameBarColor(Debt originalDebt, DebtStatus status) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final entryDate = DateTime(
      originalDebt.entryDate.year,
      originalDebt.entryDate.month,
      originalDebt.entryDate.day,
    );

    // إذا كان الدين منتهي الصلاحية (متأخر)
    final isOverdue = DateTime.now().isAfter(originalDebt.dueDate) &&
        status != DebtStatus.paid;
    if (isOverdue) {
      return const Color(0xFF8B0000); // أحمر دم
    }

    // إذا كان قريب من الموعد (خلال 3 أيام)
    final daysUntilDue = originalDebt.dueDate.difference(now).inDays;
    if (daysUntilDue <= 3 && daysUntilDue >= 0 && status != DebtStatus.paid) {
      return const Color(0xFFFF8C00); // برتقالي
    }

    // حسب تاريخ البيع
    if (entryDate.isAtSameMomentAs(today)) {
      return const Color(0xFF4CAF50); // اليوم - أخضر
    } else if (entryDate.isAtSameMomentAs(yesterday)) {
      return const Color(0xFF1A237E); // أمس - أزرق مائل للأسود
    } else {
      return const Color(0xFF2196F3); // افتراضي - أزرق
    }
  }

  // إظهار نافذة خيارات الدين عند الضغط المطول
  void _showDebtOptionsDialog(
    BuildContext context,
    Debt debt,
    Debt currentDebt,
  ) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Colors.blue.shade50],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.blue.shade600, Colors.blue.shade700],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.settings,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'خيارات الدين',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          debt.itemName,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // معلومات الدين
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'المبلغ المتبقي:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      NumberFormat(
                        '#,##0.00',
                      ).format(currentDebt.remainingAmount),
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // الأزرار
              Row(
                children: [
                  // زر التسديد
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _startMultiSelectForPayment(context, debt);
                      },
                      icon: const Icon(Icons.payment, size: 20),
                      label: const Text('تسديد محدد'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green.shade600,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 4,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // زر الحذف
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _startMultiSelectForDelete(context, debt);
                      },
                      icon: const Icon(Icons.delete, size: 20),
                      label: const Text('حذف محدد'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red.shade600,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 4,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // زر الإلغاء
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'إلغاء',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بدء وضع التحديد المتعدد للتسديد
  void _startMultiSelectForPayment(BuildContext context, Debt debt) {
    if (onStartMultiSelectForPayment != null) {
      onStartMultiSelectForPayment!();
    }
  }

  // بدء وضع التحديد المتعدد للحذف
  void _startMultiSelectForDelete(BuildContext context, Debt debt) {
    if (onStartMultiSelectForDelete != null) {
      onStartMultiSelectForDelete!();
    }
  }

  // دالة عرض تفاصيل الدين في نافذة منبثقة
  void _showDebtDetailsModal(
    BuildContext context,
    Debt debt,
    Debt currentDebt,
  ) {
    final customer = Provider.of<CustomerProvider>(context, listen: false)
        .customers
        .firstWhere(
          (c) => c.id == debt.customerId,
          orElse: () => Customer(
            id: debt.customerId,
            name: 'عميل غير موجود',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // رأس النافذة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.blue.shade800],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.receipt_long, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تفاصيل الدين',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          customer.name,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // محتوى النافذة
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // بطاقة الدين بحجم كامل
                    SizedBox(
                      width: double.infinity,
                      child: DebtCard(debt: debt),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
