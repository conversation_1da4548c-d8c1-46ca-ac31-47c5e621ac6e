import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../screens/payment_details_screen.dart';
import '../utils/number_formatter.dart';

class PaymentCard extends StatefulWidget {
  const PaymentCard({
    super.key,
    required this.payment,
    required this.customer,
    this.debt,
    this.onDelete,
  });
  final Payment payment;
  final Customer customer;
  final Debt? debt;
  final VoidCallback? onDelete;

  @override
  State<PaymentCard> createState() => _PaymentCardState();
}

class _PaymentCardState extends State<PaymentCard> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    // تحديث العداد كل 30 ثانية للدقة العالية
    _timer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dayFormat = DateFormat('EEEE', 'ar');

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time Counters at the top
            if (widget.debt != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.shade200, width: 1.5),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildTimeCounterWithSubtext(
                        'استغرق الأيام',
                        _getDaysFromEntryToPayment(),
                        _getOverdueInfo(),
                        Icons.event_available,
                        Colors.blue,
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 40,
                      color: Colors.green.shade200,
                    ),
                    Expanded(
                      child: _buildTimeCounter(
                        'منذ التسديد',
                        _getDaysSincePayment(),
                        Icons.verified,
                        Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],

            // رسالة خاصة للتسديد في نفس اليوم
            if (widget.debt != null && _isSameDayPayment()) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.purple.withValues(alpha: 0.1),
                      Colors.purple.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.purple.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.purple.withValues(alpha: 0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.purple.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.swap_horiz,
                        color: Colors.purple.shade600,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'عملية استبدال',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.purple.shade600,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            _buildReplacementMessage(),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.purple.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.purple.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        '🔄',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.purple.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Header
            Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Icon(
                        Icons.inventory_2,
                        size: 18,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.debt != null
                            ? 'الكمية: ${NumberFormatter.formatNumber(widget.debt!.quantity)}'
                            : 'تسديد',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF2C3E50),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.3),
                    ),
                  ),
                  child: const Text(
                    'مدفوع',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Amount Info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  if (widget.debt != null) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'المبلغ الإجمالي:',
                          style: TextStyle(fontSize: 14, color: Colors.black87),
                        ),
                        Text(
                          NumberFormatter.formatCurrency(widget.debt!.amount),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'المبلغ المدفوع:',
                        style: TextStyle(fontSize: 14, color: Colors.black87),
                      ),
                      Text(
                        NumberFormatter.formatCurrency(widget.payment.amount),
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  if (widget.debt != null &&
                      widget.payment.type == PaymentType.partial) ...[
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'المبلغ المتبقي:',
                          style: TextStyle(fontSize: 14, color: Colors.black87),
                        ),
                        Text(
                          NumberFormatter.formatCurrency(
                            widget.debt!.remainingAmount,
                          ),
                          style: const TextStyle(
                            color: Colors.orange,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Card Type and Dates
            if (widget.debt != null) ...[
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getCardTypeColor(
                        widget.debt!.cardType,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _getCardTypeColor(
                          widget.debt!.cardType,
                        ).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Consumer<CardTypeProvider>(
                      builder: (context, cardTypeProvider, _) {
                        final cardTypeOption = cardTypeProvider.getCardTypeById(
                          widget.debt!.cardType,
                        );
                        final displayName =
                            cardTypeOption?.displayName ?? 'نوع غير معروف';
                        return Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getCardTypeIcon(widget.debt!.cardType),
                              size: 16,
                              color: _getCardTypeColor(widget.debt!.cardType),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              displayName,
                              style: TextStyle(
                                color: _getCardTypeColor(widget.debt!.cardType),
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  const Text(
                                    'تاريخ القيد:',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: Colors.black54,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Text(
                                        dayFormat.format(
                                          widget.debt!.entryDate,
                                        ),
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.blue,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        _formatDateArabic(
                                          widget.debt!.entryDate,
                                        ),
                                        style: const TextStyle(
                                          fontSize: 13,
                                          color: Colors.black87,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Text(
                                    _formatTime(widget.debt!.entryDate),
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.black54,
                                    ),
                                    textAlign: TextAlign.end,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 6),
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.blue.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: const Icon(
                                Icons.event_note,
                                size: 16,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  const Text(
                                    'تاريخ الاستحقاق:',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: Colors.black54,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Text(
                                        dayFormat.format(widget.debt!.dueDate),
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.orange,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        _formatDateArabic(widget.debt!.dueDate),
                                        style: const TextStyle(
                                          fontSize: 13,
                                          color: Colors.black87,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Text(
                                    _formatTime(widget.debt!.dueDate),
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.black54,
                                    ),
                                    textAlign: TextAlign.end,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 6),
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.orange.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: const Icon(
                                Icons.schedule,
                                size: 16,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],

            // Payment Notes
            if (widget.payment.notes != null &&
                widget.payment.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.amber.shade200),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.amber.shade100,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        Icons.note_alt,
                        size: 16,
                        color: Colors.amber.shade700,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'ملاحظات:',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.amber.shade700,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.payment.notes!,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.black87,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                IconButton(
                  onPressed: () => _editPayment(context),
                  icon: const Icon(Icons.edit),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.blue.withValues(alpha: 0.1),
                  ),
                ),
                IconButton(
                  onPressed: () => _showDeletePaymentDialog(context),
                  icon: const Icon(Icons.delete),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.red.withValues(alpha: 0.1),
                  ),
                ),
                IconButton(
                  onPressed: () => _showReversePaymentDialog(context),
                  icon: const Icon(Icons.undo),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.orange.withValues(alpha: 0.1),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _navigateToPaymentDetails(context),
                    icon: const Icon(Icons.info_outline, size: 18),
                    label: const Text('المزيد'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // دالة لبناء عداد الوقت مع تصميم جميل ومتكيف
  Widget _buildTimeCounter(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.15),
                color.withValues(alpha: 0.08),
                Colors.white.withValues(alpha: 0.95),
              ],
              stops: const [0.0, 0.4, 1.0],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: color.withValues(alpha: 0.25),
              width: 1.2,
            ),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.15),
                blurRadius: 6,
                offset: const Offset(0, 3),
                spreadRadius: 0.5,
              ),
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.9),
                blurRadius: 2,
                offset: const Offset(-1, -1),
              ),
            ],
          ),
          child: Icon(icon, size: 16, color: color.withValues(alpha: 0.9)),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // دالة لبناء عداد الوقت مع نص فرعي وتصميم جميل
  Widget _buildTimeCounterWithSubtext(
    String label,
    String value,
    String subtext,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.15),
                color.withValues(alpha: 0.08),
                Colors.white.withValues(alpha: 0.95),
              ],
              stops: const [0.0, 0.4, 1.0],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: color.withValues(alpha: 0.25),
              width: 1.2,
            ),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.15),
                blurRadius: 6,
                offset: const Offset(0, 3),
                spreadRadius: 0.5,
              ),
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.9),
                blurRadius: 2,
                offset: const Offset(-1, -1),
              ),
            ],
          ),
          child: Icon(icon, size: 16, color: color.withValues(alpha: 0.9)),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        if (subtext.isNotEmpty) ...[
          const SizedBox(height: 2),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
            decoration: BoxDecoration(
              color: _getSubtextColor(subtext).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: _getSubtextColor(subtext).withValues(alpha: 0.3),
                width: 0.5,
              ),
            ),
            child: Text(
              subtext,
              style: TextStyle(
                fontSize: 9,
                color: _getSubtextColor(subtext),
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ],
    );
  }

  // دالة للتحقق من أن العميل سدد وأخذ دين آخر في نفس اليوم
  bool _isSameDayPayment() {
    if (widget.debt == null) return false;

    final paymentDate = DateTime(
      widget.payment.paymentDate.year,
      widget.payment.paymentDate.month,
      widget.payment.paymentDate.day,
    );

    // التحقق من وجود ديون أخرى للعميل في نفس يوم التسديد
    return _hasOtherDebtsOnSameDay(paymentDate);
  }

  // دالة للتحقق من وجود دين آخر بنفس نوع الكارت تم إضافته بعد التسديد (عملية استبدال حقيقية)
  bool _hasOtherDebtsOnSameDay(DateTime paymentDate) {
    try {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final customerDebts = debtProvider.debts
          .where((debt) => debt.customerId == widget.customer.id)
          .toList();

      // البحث عن ديون بنفس نوع الكارت تم إضافتها بعد التسديد في نفس اليوم
      final replacementDebtsOnSameDay = customerDebts.where((debt) {
        if (debt.id == widget.debt?.id) return false; // تجاهل الدين الحالي
        if (widget.debt == null) return false;

        // التحقق من نفس نوع الكارت
        if (debt.cardType != widget.debt!.cardType) return false;

        final debtDate = DateTime(
          debt.entryDate.year,
          debt.entryDate.month,
          debt.entryDate.day,
        );

        // التحقق من أن الدين في نفس يوم التسديد
        if (debtDate.difference(paymentDate).inDays != 0) return false;

        // التحقق من أن الدين تم إضافته بعد التسديد (بالوقت)
        // إذا كان تاريخ إنشاء الدين بعد تاريخ التسديد، فهو استبدال
        return debt.createdAt.isAfter(widget.payment.paymentDate);
      }).toList();

      return replacementDebtsOnSameDay.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // دالة لبناء رسالة الاستبدال مع تفاصيل الكارت المأخوذ
  String _buildReplacementMessage() {
    final replacementInfo = _getReplacementDebtInfo();
    if (replacementInfo.isNotEmpty) {
      return 'مسدد وأخذ $replacementInfo';
    }
    return 'مسدد وأخذ آخر مكانه';
  }

  // دالة للحصول على معلومات الدين المأخوذ (نوع الكارت والكمية)
  String _getReplacementDebtInfo() {
    try {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final customerDebts = debtProvider.debts
          .where((debt) => debt.customerId == widget.customer.id)
          .toList();

      final paymentDate = widget.payment.paymentDate;

      // البحث عن الدين المأخوذ
      final replacementDebt = customerDebts.where((debt) {
        if (debt.id == widget.debt?.id) return false;
        if (widget.debt == null) return false;

        if (debt.cardType != widget.debt!.cardType) return false;

        final debtDate = DateTime(
          debt.entryDate.year,
          debt.entryDate.month,
          debt.entryDate.day,
        );

        if (debtDate.difference(paymentDate).inDays != 0) return false;

        return debt.createdAt.isAfter(widget.payment.paymentDate);
      }).toList();

      if (replacementDebt.isNotEmpty) {
        final debt = replacementDebt.first;
        final cardTypeProvider =
            Provider.of<CardTypeProvider>(context, listen: false);
        final cardTypeOption = cardTypeProvider.getCardTypeById(debt.cardType);
        final cardTypeName = cardTypeOption?.displayName ?? 'غير معروف';

        return '$cardTypeName (${debt.quantity})';
      }

      return '';
    } catch (e) {
      return '';
    }
  }

  // دالة لحساب كم يوم استغرق من القيد حتى التسديد
  String _getDaysFromEntryToPayment() {
    if (widget.debt == null) return '0 يوم';

    final entryDate = DateTime(
      widget.debt!.entryDate.year,
      widget.debt!.entryDate.month,
      widget.debt!.entryDate.day,
    );
    final paymentDate = DateTime(
      widget.payment.paymentDate.year,
      widget.payment.paymentDate.month,
      widget.payment.paymentDate.day,
    );

    final daysFromEntry = paymentDate.difference(entryDate).inDays;

    if (daysFromEntry == 0) {
      return 'نفس اليوم';
    } else if (daysFromEntry == 1) {
      return 'يوم واحد';
    } else if (daysFromEntry == 2) {
      return 'يومان';
    } else {
      return '$daysFromEntry يوم';
    }
  }

  // دالة لتحديد لون النص الفرعي
  Color _getSubtextColor(String subtext) {
    if (subtext.contains('تجاوز الموعد') || subtext.contains('متأخر')) {
      return Colors.red[600]!;
    } else if (subtext.contains('في الموعد')) {
      return Colors.blue[600]!;
    } else if (subtext.contains('قبل الموعد') || subtext.contains('مبكر')) {
      return Colors.green[600]!;
    } else if (subtext.contains('استبدال')) {
      return Colors.purple[600]!; // لون مميز لعملية الاستبدال
    } else if (subtext.contains('نفس اليوم')) {
      return Colors.orange[600]!; // لون للتسديد في نفس اليوم العادي
    }
    return Colors.grey[600]!;
  }

  // دالة لإظهار معلومات تجاوز الموعد
  String _getOverdueInfo() {
    if (widget.debt == null) return '';

    final entryDate = DateTime(
      widget.debt!.entryDate.year,
      widget.debt!.entryDate.month,
      widget.debt!.entryDate.day,
    );
    final dueDate = DateTime(
      widget.debt!.dueDate.year,
      widget.debt!.dueDate.month,
      widget.debt!.dueDate.day,
    );
    final paymentDate = DateTime(
      widget.payment.paymentDate.year,
      widget.payment.paymentDate.month,
      widget.payment.paymentDate.day,
    );

    // التحقق من أن القيد والتسديد في نفس اليوم
    final sameDay = entryDate.difference(paymentDate).inDays == 0;
    final hasOtherDebts = _hasOtherDebtsOnSameDay(paymentDate);

    final daysOverdue = paymentDate.difference(dueDate).inDays;

    if (sameDay) {
      // إذا كان القيد والتسديد في نفس اليوم
      if (hasOtherDebts) {
        // إذا كان هناك استبدال (دين آخر بنفس النوع في نفس اليوم)
        if (daysOverdue > 0) {
          return 'استبدال - متأخر';
        } else if (daysOverdue == 0) {
          return 'استبدال - في الموعد';
        } else {
          return 'استبدال - مبكر';
        }
      } else {
        if (daysOverdue > 0) {
          return 'نفس اليوم - متأخر';
        } else if (daysOverdue == 0) {
          return 'نفس اليوم - في الموعد';
        } else {
          return 'نفس اليوم - مبكر';
        }
      }
    } else {
      // الحالات العادية
      if (daysOverdue > 0) {
        if (daysOverdue == 1) {
          return 'تجاوز الموعد يوم';
        } else {
          return 'تجاوز الموعد $daysOverdue يوم';
        }
      } else if (daysOverdue == 0) {
        return 'في الموعد';
      } else {
        // تم التسديد قبل الموعد
        final daysBefore = daysOverdue.abs();
        if (daysBefore == 1) {
          return 'قبل الموعد بيوم';
        } else {
          return 'قبل الموعد بـ $daysBefore يوم';
        }
      }
    }
  }

  // دالة لحساب الوقت منذ التسديد
  String _getDaysSincePayment() {
    final now = DateTime.now();
    final paymentDate = widget.payment.paymentDate;

    final difference = now.difference(paymentDate);

    final days = difference.inDays;
    final hours = difference.inHours % 24;
    final minutes = difference.inMinutes % 60;

    if (days == 0) {
      if (hours == 0) {
        if (minutes == 0) {
          return 'الآن';
        } else if (minutes == 1) {
          return 'منذ دقيقة';
        } else if (minutes == 2) {
          return 'منذ دقيقتين';
        } else if (minutes <= 10) {
          return 'منذ $minutes دقائق';
        } else {
          return 'منذ $minutes دقيقة';
        }
      } else if (hours == 1) {
        return 'منذ ساعة';
      } else if (hours == 2) {
        return 'منذ ساعتين';
      } else if (hours <= 10) {
        return 'منذ $hours ساعات';
      } else {
        return 'منذ $hours ساعة';
      }
    } else if (days == 1) {
      return 'أمس';
    } else if (days == 2) {
      return 'أول أمس';
    } else if (days <= 7) {
      return 'منذ $days أيام';
    } else if (days <= 30) {
      return 'منذ $days يوماً';
    } else if (days <= 365) {
      final weeks = (days / 7).floor();
      if (weeks == 1) {
        return 'منذ أسبوع';
      } else if (weeks == 2) {
        return 'منذ أسبوعين';
      } else if (weeks <= 10) {
        return 'منذ $weeks أسابيع';
      } else {
        return 'منذ $weeks أسبوع';
      }
    } else {
      final years = (days / 365).floor();
      if (years == 1) {
        return 'منذ سنة';
      } else if (years == 2) {
        return 'منذ سنتين';
      } else {
        return 'منذ $years سنوات';
      }
    }
  }

  // دالة لتحديد لون نوع الكارت
  Color _getCardTypeColor(String cardType) {
    switch (cardType) {
      case 'abo_setta':
        return Colors.grey;
      case 'asia':
        return Colors.red;
      case 'zain':
        return Colors.purple;
      case 'abo_ashara':
        return Colors.cyan;
      default:
        return Colors.blue;
    }
  }

  // دالة لتحديد أيقونة نوع الكارت
  IconData _getCardTypeIcon(String cardType) {
    switch (cardType) {
      case 'abo_setta':
      case 'asia':
      case 'zain':
      case 'abo_ashara':
        return Icons.credit_card;
      default:
        return Icons.credit_card;
    }
  }

  // دالة لتنسيق التاريخ بالعربية
  String _formatDateArabic(DateTime date) {
    return '${date.day}\\${date.month}\\${date.year}';
  }

  // دالة لتنسيق الوقت
  String _formatTime(DateTime date) {
    final hour = date.hour > 12
        ? date.hour - 12
        : date.hour == 0
            ? 12
            : date.hour;
    final period = date.hour >= 12 ? 'م' : 'ص';
    return '${hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')} $period';
  }

  void _editPayment(BuildContext context) {
    _showEditPaymentDialog(context);
  }

  void _showEditPaymentDialog(BuildContext context) {
    final amountController = TextEditingController(
      text: widget.payment.amount.toStringAsFixed(0),
    );
    final notesController = TextEditingController(
      text: widget.payment.notes ?? '',
    );
    DateTime selectedPaymentDate = widget.payment.paymentDate;
    DateTime selectedEntryDate = widget.debt?.entryDate ?? DateTime.now();
    DateTime selectedDueDate = widget.debt?.dueDate ?? DateTime.now();

    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 20,
          backgroundColor: Colors.white,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            constraints: const BoxConstraints(maxWidth: 450, maxHeight: 600),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade300),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.blue, Colors.blue.shade600],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.edit,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Text(
                            'تعديل التسديد',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2C3E50),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.grey.shade100,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Amount Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                      child: TextField(
                        controller: amountController,
                        keyboardType: TextInputType.number,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        decoration: InputDecoration(
                          labelText: 'مبلغ التسديد',
                          labelStyle: const TextStyle(
                            color: Colors.blue,
                            fontWeight: FontWeight.w600,
                          ),
                          suffixText: 'د.ع',
                          suffixStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.bold,
                          ),
                          prefixIcon: const Icon(
                            Icons.attach_money,
                            color: Colors.blue,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(16),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Entry Date Field
                    if (widget.debt != null) ...[
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey.shade300),
                          color: Colors.white,
                        ),
                        child: ListTile(
                          leading: const Icon(
                            Icons.event_note,
                            color: Colors.blue,
                          ),
                          title: const Text(
                            'تاريخ القيد',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.blue,
                            ),
                          ),
                          subtitle: Text(
                            DateFormat(
                              'yyyy/MM/dd - EEEE',
                              'ar',
                            ).format(selectedEntryDate),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          onTap: () async {
                            final pickedDate = await showDatePicker(
                              context: context,
                              initialDate: selectedEntryDate,
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now().add(
                                const Duration(days: 365),
                              ),
                              locale: const Locale('ar'),
                            );
                            if (pickedDate != null) {
                              setState(() {
                                selectedEntryDate = DateTime(
                                  pickedDate.year,
                                  pickedDate.month,
                                  pickedDate.day,
                                  selectedEntryDate.hour,
                                  selectedEntryDate.minute,
                                );
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Due Date Field
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey.shade300),
                          color: Colors.white,
                        ),
                        child: ListTile(
                          leading: const Icon(
                            Icons.schedule,
                            color: Colors.orange,
                          ),
                          title: const Text(
                            'تاريخ الاستحقاق',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.orange,
                            ),
                          ),
                          subtitle: Text(
                            DateFormat(
                              'yyyy/MM/dd - EEEE',
                              'ar',
                            ).format(selectedDueDate),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          onTap: () async {
                            final pickedDate = await showDatePicker(
                              context: context,
                              initialDate: selectedDueDate,
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now().add(
                                const Duration(days: 365),
                              ),
                              locale: const Locale('ar'),
                            );
                            if (pickedDate != null) {
                              setState(() {
                                selectedDueDate = DateTime(
                                  pickedDate.year,
                                  pickedDate.month,
                                  pickedDate.day,
                                  selectedDueDate.hour,
                                  selectedDueDate.minute,
                                );
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Payment Date Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                      child: ListTile(
                        leading: const Icon(
                          Icons.calendar_today,
                          color: Colors.green,
                        ),
                        title: const Text(
                          'تاريخ التسديد',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.green,
                          ),
                        ),
                        subtitle: Text(
                          DateFormat(
                            'yyyy/MM/dd - EEEE',
                            'ar',
                          ).format(selectedPaymentDate),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        onTap: () async {
                          final pickedDate = await showDatePicker(
                            context: context,
                            initialDate: selectedPaymentDate,
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now().add(
                              const Duration(days: 365),
                            ),
                            locale: const Locale('ar'),
                          );
                          if (pickedDate != null) {
                            setState(() {
                              selectedPaymentDate = DateTime(
                                pickedDate.year,
                                pickedDate.month,
                                pickedDate.day,
                                selectedPaymentDate.hour,
                                selectedPaymentDate.minute,
                              );
                            });
                          }
                        },
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Notes Field
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        color: Colors.white,
                      ),
                      child: TextField(
                        controller: notesController,
                        maxLines: 3,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          labelStyle: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                          hintText: 'أضف أي ملاحظات حول التسديد...',
                          hintStyle: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 15,
                          ),
                          prefixIcon: Icon(
                            Icons.note_alt_outlined,
                            color: Colors.grey[600],
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(16),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              side: BorderSide(color: Colors.grey.shade400),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'إلغاء',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => _updatePayment(
                              context,
                              amountController.text,
                              notesController.text,
                              selectedPaymentDate,
                              selectedEntryDate,
                              selectedDueDate,
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: const Text(
                              'حفظ التعديل',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _updatePayment(
    BuildContext context,
    String amountText,
    String notes,
    DateTime paymentDate,
    DateTime entryDate,
    DateTime dueDate,
  ) async {
    try {
      // التحقق من صحة المبلغ
      final amount = double.tryParse(amountText);
      if (amount == null || amount <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى إدخال مبلغ صحيح'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // إنشاء تسديد محدث
      final updatedPayment = Payment(
        id: widget.payment.id,
        debtId: widget.payment.debtId,
        customerId: widget.payment.customerId,
        amount: amount,
        paymentDate: paymentDate,
        notes: notes.isEmpty ? null : notes,
        type: widget.payment.type,
        createdAt: widget.payment.createdAt,
        updatedAt: DateTime.now(),
      );

      // تحديث التسديد في قاعدة البيانات
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      await debtProvider.updatePayment(updatedPayment);

      // تحديث الدين إذا كان موجود
      if (widget.debt != null) {
        final updatedDebt = Debt(
          id: widget.debt!.id,
          customerId: widget.debt!.customerId,
          amount: widget.debt!.amount,
          quantity: widget.debt!.quantity,
          cardType: widget.debt!.cardType,
          itemName: widget.debt!.itemName,
          entryDate: entryDate,
          dueDate: dueDate,
          notes: widget.debt!.notes,
          status: widget.debt!.status,
          createdAt: widget.debt!.createdAt,
          updatedAt: DateTime.now(),
        );
        await debtProvider.updateDebt(updatedDebt);
      }

      if (context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث التسديد والدين بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // تحديث الواجهة
      if (widget.onDelete != null) {
        widget.onDelete!();
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeletePaymentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.red[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'حذف التسديد',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من حذف "${widget.payment.type == PaymentType.full ? 'التسديد الكامل' : 'التسديد الجزئي'}" نهائياً؟\n\nلا يمكن التراجع عن هذا الإجراء.',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          ElevatedButton(
            onPressed: () async {
              try {
                if (widget.payment.id == null) {
                  throw Exception('معرف التسديد غير صحيح');
                }

                final debtProvider = Provider.of<DebtProvider>(
                  context,
                  listen: false,
                );
                await debtProvider.deletePayment(widget.payment.id!);

                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف التسديد بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }

                if (widget.onDelete != null) {
                  widget.onDelete!();
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'حذف',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
        ],
      ),
    );
  }

  void _showReversePaymentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.undo, color: Colors.blue[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'إرجاع التسديد',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من إرجاع "${widget.payment.type == PaymentType.full ? 'التسديد الكامل' : 'التسديد الجزئي'}"؟\n\nسيتم إرجاع الدين إلى حالة غير مدفوع.',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          ElevatedButton(
            onPressed: () async {
              try {
                if (widget.payment.id == null) {
                  throw Exception('معرف التسديد غير صحيح');
                }

                final debtProvider = Provider.of<DebtProvider>(
                  context,
                  listen: false,
                );
                await debtProvider.reversePayment(widget.payment.id!);

                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إرجاع التسديد بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }

                if (widget.onDelete != null) {
                  widget.onDelete!();
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'إرجاع',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
        ],
      ),
    );
  }

  void _navigateToPaymentDetails(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentDetailsScreen(
          payment: widget.payment,
          debt: widget.debt,
          customer: widget.customer,
        ),
      ),
    );
  }
}
