@echo off
echo ========================================
echo    محاسب الديون الاحترافي - بناء APK
echo ========================================
echo.

echo [1/5] تنظيف المشروع...
flutter clean
if %errorlevel% neq 0 (
    echo خطأ في تنظيف المشروع!
    pause
    exit /b 1
)

echo.
echo [2/5] تحديث التبعيات...
flutter pub get
if %errorlevel% neq 0 (
    echo خطأ في تحديث التبعيات!
    pause
    exit /b 1
)

echo.
echo [3/5] بناء ملف APK...
flutter build apk --release
if %errorlevel% neq 0 (
    echo خطأ في بناء APK!
    pause
    exit /b 1
)

echo.
echo [4/5] نسخ ملف APK...
set timestamp=%date:~-4%%date:~3,2%%date:~0,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set timestamp=%timestamp: =0%
copy "build\app\outputs\flutter-apk\app-release.apk" "mahasb-updated-v2.0-%timestamp%.apk"
if %errorlevel% neq 0 (
    echo خطأ في نسخ الملف!
    pause
    exit /b 1
)

echo.
echo [5/5] نسخ إلى مجلد الإصدارات...
if not exist "releases" mkdir releases
copy "mahasb-updated-v2.0-%timestamp%.apk" "releases\"
if %errorlevel% neq 0 (
    echo تحذير: لم يتم نسخ الملف إلى مجلد الإصدارات
)

echo.
echo ========================================
echo           تم بناء APK بنجاح!
echo ========================================
echo اسم الملف: mahasb-updated-v2.0-%timestamp%.apk
echo المكان: %cd%
echo الحجم: 
for %%A in ("mahasb-updated-v2.0-%timestamp%.apk") do echo %%~zA bytes
echo.
echo يمكنك الآن تثبيت التطبيق على جهاز Android
echo ========================================
pause
